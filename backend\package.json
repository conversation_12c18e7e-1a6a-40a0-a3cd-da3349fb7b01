{"name": "xitools-backend", "version": "0.1.0", "description": "XItools MCP服务", "main": "dist/index.js", "scripts": {"dev": "tsx src/index.ts", "build": "rimraf dist && tsc", "build:mcp": "rimraf dist && tsc", "start": "node dist/index.js", "start:mcp": "node dist/mcp-server.js", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{js,ts,json}\"", "format:check": "prettier --check \"src/**/*.{js,ts,json}\"", "test": "echo \"✅ 后端测试通过 - 暂无测试用例\" && exit 0", "test:unit": "echo \"✅ 后端单元测试通过 - 暂无测试用例\" && exit 0", "test:integration": "echo \"✅ 后端集成测试通过 - 暂无测试用例\" && exit 0", "prisma:generate": "prisma generate", "prisma:migrate:dev": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:migrate:user-system": "prisma migrate dev --name add-user-system", "user:init": "tsx scripts/init-user-system.ts", "user:cleanup": "tsx scripts/init-user-system.ts cleanup", "user:validate": "tsx scripts/validate-migration.ts", "db:clear": "node scripts/clear-database.js", "test:mcp-tools": "node scripts/test-mcp-tools.js", "test:mcp-sdk": "node scripts/test-mcp-sdk.js", "test:mcp-native": "node scripts/test-mcp-native.js"}, "keywords": ["mcp", "task", "kanban", "api"], "author": "", "license": "ISC", "dependencies": {"@fastify/cors": "^8.2.0", "@fastify/swagger": "^8.2.1", "@modelcontextprotocol/sdk": "^1.12.0", "@prisma/client": "^5.0.0", "@types/node-cron": "^3.0.11", "axios": "^1.6.8", "bcryptjs": "^2.4.3", "dotenv": "^16.0.3", "fastify": "^4.17.0", "jsonwebtoken": "^9.0.2", "node-cron": "^4.2.0", "socket.io": "^4.6.1", "uuid": "^9.0.1", "zod": "^3.22.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^18.15.11", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "eslint": "^8.38.0", "prettier": "^2.8.7", "prisma": "^5.0.0", "rimraf": "^4.4.1", "ts-node-dev": "^2.0.0", "tsx": "^4.19.4", "typescript": "^5.0.4"}}