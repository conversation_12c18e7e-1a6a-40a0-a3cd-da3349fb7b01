# XItools系统Nginx配置
# 系统级Nginx配置，代理到Docker容器
# 使用现有的furdow.com证书，支持xitools.furdow.com子域名

# ================================
# XItools项目配置 (xitools.furdow.com)
# ================================

# HTTP - 重定向到HTTPS
server {
    listen 80;
    server_name xitools.furdow.com;
    
    # Let's Encrypt验证路径（用于证书续期）
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        allow all;
    }
    
    # 重定向到HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# HTTPS - XItools项目主配置
server {
    listen 443 ssl http2;
    server_name xitools.furdow.com;
    
    # SSL配置 - 使用现有的furdow.com证书
    ssl_certificate /etc/letsencrypt/live/furdow.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/furdow.com/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers "EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH";
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # 安全头部
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # 文件上传大小限制
    client_max_body_size 50M;
    
    # 日志配置
    access_log /var/log/nginx/xitools.access.log;
    error_log /var/log/nginx/xitools.error.log;
    
    # ================================
    # 代理到Docker容器的Nginx (端口8080)
    # ================================
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;

        # 基本代理头部
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 支持WebSocket升级
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_cache_bypass $http_upgrade;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 禁用缓冲以支持流式响应
        proxy_buffering off;
        proxy_request_buffering off;
    }
    
    # ================================
    # 安全配置
    # ================================
    
    # 隐藏nginx版本
    server_tokens off;
    
    # 防止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 防止访问备份文件
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
