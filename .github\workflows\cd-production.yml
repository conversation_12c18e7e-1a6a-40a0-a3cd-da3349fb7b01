# XItools 生产环境部署工作流
# 简化版生产部署，专注于安全可靠的部署

name: 生产环境部署

on:
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  NODE_VERSION: '20.x'

jobs:
  # 生产环境部署
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    environment: production

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 生成版本号
      id: version
      run: |
        VERSION=prod-$(date +%Y%m%d-%H%M%S)-$(git rev-parse --short HEAD)
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "📦 版本号: $VERSION"

    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: |
          frontend/package-lock.json
          backend/package-lock.json

    - name: 构建验证
      run: |
        echo "🏗️ 生产构建验证..."
        cd frontend && npm ci
        echo "📝 复制Web版本TypeScript配置..."
        cp tsconfig.web.json tsconfig.json
        echo "🔨 构建前端（Web版本）..."
        npx vite build
        cd ../backend && npm ci && npm run build
        echo "✅ 构建验证完成"

    - name: 构建并推送镜像
      run: |
        echo "🐳 构建生产环境 Docker 镜像..."
        echo ${{ secrets.GITHUB_TOKEN }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin

        docker build -t ghcr.io/${{ github.repository }}/frontend:latest ./frontend --target production
        docker push ghcr.io/${{ github.repository }}/frontend:latest

        docker build -t ghcr.io/${{ github.repository }}/backend:latest ./backend --target production
        docker push ghcr.io/${{ github.repository }}/backend:latest

        echo "✅ 镜像构建和推送完成"


    - name: 设置 SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

    - name: 部署到生产服务器
      run: |
        echo "🚀 开始部署到生产环境..."

        # 创建部署目录
        ssh -o StrictHostKeyChecking=no ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} << 'EOF'
        mkdir -p /opt/xitools/releases/${{ steps.version.outputs.version }}
        mkdir -p /opt/xitools/shared
        EOF

        # 准备配置文件
        cat > .env.production << EOF
        NODE_ENV=production
        DATABASE_URL=${{ secrets.DATABASE_URL }}
        JWT_SECRET=${{ secrets.JWT_SECRET }}
        POSTGRES_USER=${{ secrets.POSTGRES_USER }}
        POSTGRES_PASSWORD=${{ secrets.POSTGRES_PASSWORD }}
        POSTGRES_DB=${{ secrets.POSTGRES_DB }}
        VITE_BACKEND_URL=${{ secrets.VITE_BACKEND_URL }}
        CORS_ORIGINS=${{ secrets.CORS_ORIGINS }}
        EOF

        # 上传文件
        scp -o StrictHostKeyChecking=no docker-compose.prod.yml ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/opt/xitools/releases/${{ steps.version.outputs.version }}/
        scp -o StrictHostKeyChecking=no .env.production ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/opt/xitools/releases/${{ steps.version.outputs.version }}/

        # 执行部署
        ssh -o StrictHostKeyChecking=no ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} << 'EOF'
        cd /opt/xitools

        # 停止当前服务
        if [ -f current/docker-compose.prod.yml ]; then
          docker-compose -f current/docker-compose.prod.yml down || true
        fi

        # 切换到新版本
        ln -sfn releases/${{ steps.version.outputs.version }} current
        cd current

        # 启动服务
        docker-compose -f docker-compose.prod.yml pull
        docker-compose -f docker-compose.prod.yml up -d

        echo "✅ 生产环境部署完成"
        EOF

    - name: 基础健康检查
      run: |
        echo "🏥 等待服务启动并进行健康检查..."
        sleep 30

        # 简单的健康检查
        for i in {1..10}; do
          if curl -f -s --connect-timeout 5 "https://xitools.furdow.com/" > /dev/null; then
            echo "✅ 生产环境健康检查通过"
            break
          else
            echo "⏳ 等待服务启动... ($i/10)"
            if [ $i -eq 10 ]; then
              echo "⚠️ 健康检查超时，但部署已完成"
            fi
            sleep 10
          fi
        done

    - name: 部署完成通知
      if: always()
      run: |
        echo "📢 生产环境部署完成"
        echo "版本: ${{ steps.version.outputs.version }}"
        echo "分支: ${{ github.ref_name }}"
        echo "访问地址: https://xitools.furdow.com"
        echo "部署时间: $(date)"
