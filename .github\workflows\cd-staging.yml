# XItools 预生产环境部署工作流
# 简化版预生产部署，专注于快速部署和基础验证

name: 预生产环境部署

on:
  push:
    branches: [ develop ]
  workflow_dispatch:
    inputs:
      deploy_branch:
        description: '部署分支'
        required: true
        default: 'develop'
        type: string

env:
  NODE_VERSION: '20.x'
  STAGING_PORT: 8081

jobs:
  # 预生产部署
  deploy-staging:
    name: 部署到预生产环境
    runs-on: ubuntu-latest
    environment: staging

    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.inputs.deploy_branch || 'develop' }}

    - name: 生成版本号
      id: version
      run: |
        VERSION=staging-$(date +%Y%m%d-%H%M%S)-$(git rev-parse --short HEAD)
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "📦 版本号: $VERSION"

    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: |
          frontend/package-lock.json
          backend/package-lock.json

    - name: 快速构建验证
      run: |
        echo "🏗️ 快速构建验证..."
        cd frontend && npm ci
        echo "📝 复制Web版本TypeScript配置..."
        cp tsconfig.web.json tsconfig.json
        echo "🔨 构建前端（Web版本）..."
        npx vite build
        cd ../backend && npm ci && npm run build
        echo "✅ 构建验证完成"


    - name: 构建并推送镜像
      run: |
        echo "🐳 构建 Docker 镜像..."
        echo ${{ secrets.GITHUB_TOKEN }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin

        docker build -t ghcr.io/${{ github.repository }}/frontend:staging ./frontend --target production
        docker push ghcr.io/${{ github.repository }}/frontend:staging

        docker build -t ghcr.io/${{ github.repository }}/backend:staging ./backend --target production
        docker push ghcr.io/${{ github.repository }}/backend:staging

        echo "✅ 镜像构建和推送完成"

    - name: 设置 SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

    - name: 部署到服务器
      run: |
        echo "🚀 开始部署到预生产环境..."

        # 创建部署目录
        ssh -o StrictHostKeyChecking=no ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} << 'EOF'
        mkdir -p /opt/xitools-staging/releases/${{ steps.version.outputs.version }}
        mkdir -p /opt/xitools-staging/shared
        EOF

        # 准备配置文件
        cp docker-compose.prod.yml docker-compose.staging.yml
        sed -i 's/8080:80/8081:80/g' docker-compose.staging.yml
        sed -i 's/5432:5432/5433:5432/g' docker-compose.staging.yml

        # 创建环境变量文件
        cat > .env.staging << EOF
        NODE_ENV=staging
        DATABASE_URL=postgresql://${{ secrets.POSTGRES_USER }}:${{ secrets.POSTGRES_PASSWORD }}@postgres:5432/xitools_staging
        JWT_SECRET=${{ secrets.JWT_SECRET }}
        POSTGRES_USER=${{ secrets.POSTGRES_USER }}
        POSTGRES_PASSWORD=${{ secrets.POSTGRES_PASSWORD }}
        POSTGRES_DB=xitools_staging
        VITE_BACKEND_URL=http://xitools.furdow.com:8081/api
        CORS_ORIGINS=http://xitools.furdow.com:8081
        EOF

        # 上传文件
        scp -o StrictHostKeyChecking=no docker-compose.staging.yml ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/opt/xitools-staging/releases/${{ steps.version.outputs.version }}/docker-compose.prod.yml
        scp -o StrictHostKeyChecking=no .env.staging ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/opt/xitools-staging/releases/${{ steps.version.outputs.version }}/.env.production

        # 执行部署
        ssh -o StrictHostKeyChecking=no ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} << 'EOF'
        cd /opt/xitools-staging

        # 停止当前服务
        if [ -f current/docker-compose.prod.yml ]; then
          docker-compose -f current/docker-compose.prod.yml down || true
        fi

        # 切换到新版本
        ln -sfn releases/${{ steps.version.outputs.version }} current
        cd current

        # 启动服务
        docker-compose -f docker-compose.prod.yml pull
        docker-compose -f docker-compose.prod.yml up -d

        echo "✅ 预生产环境部署完成"
        EOF


    - name: 基础健康检查
      run: |
        echo "🏥 等待服务启动并进行健康检查..."
        sleep 30

        # 简单的健康检查
        for i in {1..10}; do
          if curl -f -s --connect-timeout 5 "http://xitools.furdow.com:${{ env.STAGING_PORT }}/" > /dev/null; then
            echo "✅ 预生产环境健康检查通过"
            break
          else
            echo "⏳ 等待服务启动... ($i/10)"
            if [ $i -eq 10 ]; then
              echo "⚠️ 健康检查超时，但部署已完成"
            fi
            sleep 10
          fi
        done

    - name: 部署完成通知
      if: always()
      run: |
        echo "📢 预生产环境部署完成"
        echo "版本: ${{ steps.version.outputs.version }}"
        echo "分支: ${{ github.event.inputs.deploy_branch || 'develop' }}"
        echo "访问地址: http://xitools.furdow.com:${{ env.STAGING_PORT }}"
        echo "部署时间: $(date)"
