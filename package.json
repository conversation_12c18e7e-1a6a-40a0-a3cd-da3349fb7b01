{"name": "xitools", "version": "1.0.0", "description": "XItools - 智能任务看板 (Docker版本)", "private": true, "scripts": {"dev": "node scripts/docker-env.cjs start development --build --detached", "prod": "node scripts/docker-env.cjs start production --build --detached", "docker:up:dev": "node scripts/docker-env.cjs start development --build --detached", "docker:up:prod": "node scripts/docker-env.cjs start production --build --detached", "docker:stop:dev": "node scripts/docker-env.cjs stop development", "docker:stop:prod": "node scripts/docker-env.cjs stop production", "docker:restart:dev": "node scripts/docker-env.cjs restart development", "docker:restart:prod": "node scripts/docker-env.cjs restart production", "docker:status:dev": "node scripts/docker-env.cjs status development", "docker:status:prod": "node scripts/docker-env.cjs status production", "docker:logs:dev": "node scripts/docker-env.cjs logs development", "docker:logs:prod": "node scripts/docker-env.cjs logs production", "migrate:user-system": "node scripts/migrate-user-system.cjs", "user:init": "docker-compose -f docker-compose.dev.yml exec backend npm run user:init", "user:cleanup": "docker-compose -f docker-compose.dev.yml exec backend npm run user:cleanup", "db:clear": "docker-compose -f docker-compose.dev.yml exec backend npm run db:clear", "health-check": "bash scripts/health-check.sh", "backup": "bash scripts/backup.sh", "backup:database": "bash scripts/backup.sh -t database", "rollback": "bash scripts/rollback.sh -i", "rollback:auto": "bash scripts/rollback.sh -y", "test:docker-build": "powershell -ExecutionPolicy Bypass -File scripts/test-docker-build.ps1", "test:docker-build:cleanup": "powershell -ExecutionPolicy Bypass -File scripts/test-docker-build.ps1 -Cleanup", "test:ci-build": "powershell -ExecutionPolicy Bypass -File scripts/test-ci-build.ps1", "test:ci-build:cleanup": "powershell -ExecutionPolicy Bypass -File scripts/test-ci-build.ps1 -Cleanup"}, "keywords": ["task-management", "kanban", "docker", "mcp"], "author": "XItools Team", "license": "MIT", "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/xitools.git"}, "dependencies": {"@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.10", "bcrypt": "^6.0.0", "jsonwebtoken": "^9.0.2"}, "main": ".eslintrc.js", "devDependencies": {}, "type": "commonjs", "bugs": {"url": "https://github.com/your-org/xitools/issues"}, "homepage": "https://github.com/your-org/xitools#readme"}