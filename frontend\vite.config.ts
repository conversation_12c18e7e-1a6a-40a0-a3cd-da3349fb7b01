import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '');

  // 不再需要后端URL配置，直接使用nginx

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    // Web 生产环境固定使用根路径，避免在子路由下相对路径导致静态资源404
    // Electron/Web 开发模式不受本镜像影响，生产环境总是走 Docker 构建
    base: '/',
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      // 确保CSS正确内联和提取
      cssCodeSplit: true,
      // 确保静态资源路径正确
      assetsInlineLimit: 4096,
      rollupOptions: {
        output: {
          manualChunks: undefined,
          // 确保CSS文件名包含hash以避免缓存问题
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.');
            const ext = info[info.length - 1];
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
              return `assets/images/[name]-[hash][extname]`;
            }
            if (/css/i.test(ext)) {
              return `assets/css/[name]-[hash][extname]`;
            }
            return `assets/[name]-[hash][extname]`;
          },
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
        },
      },
    },
    server: {
      port: 5173,
      host: '0.0.0.0', // 允许外部访问
      strictPort: true, // 端口被占用时不自动尝试下一个端口
      cors: true, // 启用CORS
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
      watch: {
        usePolling: true, // Docker环境下启用轮询
        interval: 1000,   // 轮询间隔
      },
      // 移除代理配置，直接使用nginx
    },
  };
});